"""
基础图像融合模块
实现加权平均、线性融合等基础融合算法
"""

import cv2
import numpy as np
from typing import Tuple, Optional, List


class BasicFusion:
    """基础图像融合器"""
    
    def __init__(self):
        """初始化基础融合器"""
        pass
    
    def weighted_average_fusion(self, left_image: np.ndarray, right_image: np.ndarray,
                               left_weight: float = 0.5) -> np.ndarray:
        """
        加权平均融合
        
        Args:
            left_image: 左侧图像
            right_image: 右侧图像
            left_weight: 左图权重 (0-1)
            
        Returns:
            融合后的图像
        """
        right_weight = 1.0 - left_weight
        
        # 确保图像尺寸一致
        if left_image.shape != right_image.shape:
            right_image = cv2.resize(right_image, (left_image.shape[1], left_image.shape[0]))
        
        # 加权融合
        fused = cv2.addWeighted(left_image, left_weight, right_image, right_weight, 0)
        
        return fused
    
    def gradient_based_fusion(self, left_image: np.ndarray, right_image: np.ndarray) -> np.ndarray:
        """
        基于梯度的融合
        
        Args:
            left_image: 左侧图像
            right_image: 右侧图像
            
        Returns:
            融合后的图像
        """
        # 确保图像尺寸一致
        if left_image.shape != right_image.shape:
            right_image = cv2.resize(right_image, (left_image.shape[1], left_image.shape[0]))
        
        # 转换为灰度图计算梯度
        gray_left = cv2.cvtColor(left_image, cv2.COLOR_BGR2GRAY)
        gray_right = cv2.cvtColor(right_image, cv2.COLOR_BGR2GRAY)
        
        # 计算梯度幅值
        grad_left = cv2.Laplacian(gray_left, cv2.CV_64F)
        grad_right = cv2.Laplacian(gray_right, cv2.CV_64F)
        
        grad_left = np.abs(grad_left)
        grad_right = np.abs(grad_right)
        
        # 创建权重掩码
        weight_mask = (grad_left > grad_right).astype(np.float32)
        
        # 扩展到3通道
        weight_mask = np.stack([weight_mask] * 3, axis=2)
        
        # 融合
        fused = weight_mask * left_image + (1 - weight_mask) * right_image
        
        return fused.astype(np.uint8)
    
    def seamless_fusion(self, left_image: np.ndarray, right_image: np.ndarray,
                       blend_width: int = 50) -> np.ndarray:
        """
        无缝融合（在边界处平滑过渡）
        
        Args:
            left_image: 左侧图像
            right_image: 右侧图像
            blend_width: 融合边界宽度
            
        Returns:
            融合后的图像
        """
        # 确保图像尺寸一致
        if left_image.shape != right_image.shape:
            right_image = cv2.resize(right_image, (left_image.shape[1], left_image.shape[0]))
        
        h, w = left_image.shape[:2]
        
        # 创建融合掩码
        mask = np.zeros((h, w), dtype=np.float32)
        
        # 左半部分使用左图
        mask[:, :w//2 - blend_width//2] = 1.0
        
        # 中间部分平滑过渡
        for i in range(blend_width):
            col = w//2 - blend_width//2 + i
            if 0 <= col < w:
                weight = 1.0 - (i / blend_width)
                mask[:, col] = weight
        
        # 扩展到3通道
        mask = np.stack([mask] * 3, axis=2)
        
        # 融合
        fused = mask * left_image + (1 - mask) * right_image
        
        return fused.astype(np.uint8)
    
    def adaptive_fusion(self, left_image: np.ndarray, right_image: np.ndarray,
                       landmarks_left: Optional[np.ndarray] = None,
                       landmarks_right: Optional[np.ndarray] = None) -> np.ndarray:
        """
        自适应融合（根据面部区域调整融合策略）
        
        Args:
            left_image: 左侧图像
            right_image: 右侧图像
            landmarks_left: 左图关键点
            landmarks_right: 右图关键点
            
        Returns:
            融合后的图像
        """
        # 确保图像尺寸一致
        if left_image.shape != right_image.shape:
            right_image = cv2.resize(right_image, (left_image.shape[1], left_image.shape[0]))
        
        # 如果没有关键点，使用基础加权融合
        if landmarks_left is None or landmarks_right is None:
            return self.weighted_average_fusion(left_image, right_image)
        
        # 否则使用无缝融合
        return self.seamless_fusion(left_image, right_image)


def test_basic_fusion():
    """测试基础融合器"""
    # 创建测试图像
    left_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    right_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    
    fusion = BasicFusion()
    
    # 测试各种融合方法
    print("测试加权平均融合...")
    weighted_result = fusion.weighted_average_fusion(left_image, right_image, 0.6)
    print(f"加权融合结果尺寸: {weighted_result.shape}")
    
    print("测试梯度融合...")
    gradient_result = fusion.gradient_based_fusion(left_image, right_image)
    print(f"梯度融合结果尺寸: {gradient_result.shape}")
    
    print("测试无缝融合...")
    seamless_result = fusion.seamless_fusion(left_image, right_image)
    print(f"无缝融合结果尺寸: {seamless_result.shape}")
    
    print("基础融合器测试完成")


if __name__ == "__main__":
    test_basic_fusion()

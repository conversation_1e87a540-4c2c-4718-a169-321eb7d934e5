"""
人脸检测模块
使用多种方法进行人脸检测，包括 dlib、OpenCV 和 face_recognition
"""

import cv2
import dlib
import numpy as np
import face_recognition
from typing import List, Tuple, Optional, Union
import os


class FaceDetector:
    """人脸检测器类"""
    
    def __init__(self, method='dlib'):
        """
        初始化人脸检测器
        
        Args:
            method: 检测方法 ('dlib', 'opencv', 'face_recognition')
        """
        self.method = method
        self.detector = None
        self.predictor = None
        
        self._initialize_detector()
    
    def _initialize_detector(self):
        """初始化检测器"""
        if self.method == 'dlib':
            self.detector = dlib.get_frontal_face_detector()
            # 下载并加载68点面部关键点预测器
            predictor_path = self._get_predictor_path()
            if predictor_path and os.path.exists(predictor_path):
                self.predictor = dlib.shape_predictor(predictor_path)
            else:
                print("警告: 未找到dlib面部关键点预测器文件")
                
        elif self.method == 'opencv':
            # 使用OpenCV的Haar级联分类器
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.detector = cv2.CascadeClassifier(cascade_path)
            
        elif self.method == 'face_recognition':
            # face_recognition库内置检测器
            pass
        else:
            raise ValueError(f"不支持的检测方法: {self.method}")
    
    def _get_predictor_path(self) -> Optional[str]:
        """获取dlib预测器文件路径"""
        # 常见的预测器文件位置
        possible_paths = [
            'shape_predictor_68_face_landmarks.dat',
            'models/shape_predictor_68_face_landmarks.dat',
            os.path.expanduser('~/shape_predictor_68_face_landmarks.dat'),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def detect_faces(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        检测图像中的人脸
        
        Args:
            image: 输入图像 (BGR格式)
            
        Returns:
            人脸边界框列表 [(x, y, w, h), ...]
        """
        if self.method == 'dlib':
            return self._detect_faces_dlib(image)
        elif self.method == 'opencv':
            return self._detect_faces_opencv(image)
        elif self.method == 'face_recognition':
            return self._detect_faces_face_recognition(image)
    
    def _detect_faces_dlib(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """使用dlib检测人脸"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.detector(gray)
        
        face_boxes = []
        for face in faces:
            x, y, w, h = face.left(), face.top(), face.width(), face.height()
            face_boxes.append((x, y, w, h))
        
        return face_boxes
    
    def _detect_faces_opencv(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """使用OpenCV检测人脸"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.detector.detectMultiScale(
            gray, 
            scaleFactor=1.1, 
            minNeighbors=5, 
            minSize=(30, 30)
        )
        
        return [(x, y, w, h) for x, y, w, h in faces]
    
    def _detect_faces_face_recognition(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """使用face_recognition检测人脸"""
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        face_locations = face_recognition.face_locations(rgb_image)
        
        face_boxes = []
        for top, right, bottom, left in face_locations:
            w, h = right - left, bottom - top
            face_boxes.append((left, top, w, h))
        
        return face_boxes
    
    def get_largest_face(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        获取图像中最大的人脸
        
        Args:
            image: 输入图像
            
        Returns:
            最大人脸的边界框 (x, y, w, h) 或 None
        """
        faces = self.detect_faces(image)
        if not faces:
            return None
        
        # 找到面积最大的人脸
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        return largest_face
    
    def draw_faces(self, image: np.ndarray, faces: List[Tuple[int, int, int, int]]) -> np.ndarray:
        """
        在图像上绘制人脸边界框
        
        Args:
            image: 输入图像
            faces: 人脸边界框列表
            
        Returns:
            绘制了边界框的图像
        """
        result = image.copy()
        for x, y, w, h in faces:
            cv2.rectangle(result, (x, y), (x + w, y + h), (0, 255, 0), 2)
        return result


def test_face_detector():
    """测试人脸检测器"""
    # 创建一个测试图像（纯色图像用于演示）
    test_image = np.zeros((480, 640, 3), dtype=np.uint8)
    test_image[:] = (128, 128, 128)  # 灰色背景
    
    # 测试不同的检测方法
    methods = ['opencv', 'face_recognition']  # dlib需要额外的模型文件
    
    for method in methods:
        try:
            detector = FaceDetector(method=method)
            faces = detector.detect_faces(test_image)
            print(f"{method} 检测器初始化成功，检测到 {len(faces)} 个人脸")
        except Exception as e:
            print(f"{method} 检测器初始化失败: {e}")


if __name__ == "__main__":
    test_face_detector()

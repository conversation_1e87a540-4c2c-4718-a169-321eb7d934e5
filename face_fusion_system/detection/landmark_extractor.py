"""
面部关键点提取模块
提取68个面部关键点，用于后续的几何变换和对齐
"""

import cv2
import dlib
import numpy as np
import face_recognition
from typing import List, Tuple, Optional, Dict
import os


class LandmarkExtractor:
    """面部关键点提取器"""
    
    def __init__(self, method='dlib'):
        """
        初始化关键点提取器
        
        Args:
            method: 提取方法 ('dlib', 'face_recognition')
        """
        self.method = method
        self.predictor = None
        self.detector = None
        
        self._initialize_extractor()
    
    def _initialize_extractor(self):
        """初始化提取器"""
        if self.method == 'dlib':
            self.detector = dlib.get_frontal_face_detector()
            predictor_path = self._get_predictor_path()
            if predictor_path and os.path.exists(predictor_path):
                self.predictor = dlib.shape_predictor(predictor_path)
            else:
                print("警告: 未找到dlib面部关键点预测器文件")
                print("请下载 shape_predictor_68_face_landmarks.dat 文件")
                
        elif self.method == 'face_recognition':
            # face_recognition库内置关键点提取
            pass
        else:
            raise ValueError(f"不支持的提取方法: {self.method}")
    
    def _get_predictor_path(self) -> Optional[str]:
        """获取dlib预测器文件路径"""
        possible_paths = [
            'shape_predictor_68_face_landmarks.dat',
            'models/shape_predictor_68_face_landmarks.dat',
            os.path.expanduser('~/shape_predictor_68_face_landmarks.dat'),
            'face_fusion_system/models/shape_predictor_68_face_landmarks.dat',
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def extract_landmarks(self, image: np.ndarray, face_box: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """
        提取面部关键点
        
        Args:
            image: 输入图像
            face_box: 人脸边界框 (x, y, w, h)
            
        Returns:
            68个关键点坐标 shape: (68, 2) 或 None
        """
        if self.method == 'dlib':
            return self._extract_landmarks_dlib(image, face_box)
        elif self.method == 'face_recognition':
            return self._extract_landmarks_face_recognition(image, face_box)
    
    def _extract_landmarks_dlib(self, image: np.ndarray, face_box: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """使用dlib提取关键点"""
        if self.predictor is None:
            return None
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        x, y, w, h = face_box
        
        # 创建dlib rectangle对象
        rect = dlib.rectangle(x, y, x + w, y + h)
        
        # 提取关键点
        landmarks = self.predictor(gray, rect)
        
        # 转换为numpy数组
        points = np.array([(landmarks.part(i).x, landmarks.part(i).y) for i in range(68)])
        
        return points
    
    def _extract_landmarks_face_recognition(self, image: np.ndarray, face_box: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """使用face_recognition提取关键点"""
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        x, y, w, h = face_box
        
        # face_recognition需要的格式是 (top, right, bottom, left)
        face_location = (y, x + w, y + h, x)
        
        # 提取关键点
        landmarks_list = face_recognition.face_landmarks(rgb_image, [face_location])
        
        if not landmarks_list:
            return None
        
        landmarks = landmarks_list[0]
        
        # 将关键点转换为68点格式
        points = self._convert_to_68_points(landmarks)
        
        return points
    
    def _convert_to_68_points(self, landmarks: Dict) -> np.ndarray:
        """
        将face_recognition的关键点格式转换为68点格式
        
        Args:
            landmarks: face_recognition返回的关键点字典
            
        Returns:
            68个关键点的numpy数组
        """
        # face_recognition返回的关键点顺序
        point_order = [
            'chin',           # 0-16: 下巴轮廓
            'left_eyebrow',   # 17-21: 左眉毛
            'right_eyebrow',  # 22-26: 右眉毛
            'nose_bridge',    # 27-30: 鼻梁
            'nose_tip',       # 31-35: 鼻尖
            'left_eye',       # 36-41: 左眼
            'right_eye',      # 42-47: 右眼
            'top_lip',        # 48-54: 上唇
            'bottom_lip'      # 55-67: 下唇
        ]
        
        points = []
        for feature in point_order:
            if feature in landmarks:
                points.extend(landmarks[feature])
        
        # 确保有68个点
        while len(points) < 68:
            points.append((0, 0))
        
        return np.array(points[:68])
    
    def get_face_landmarks(self, image: np.ndarray) -> Optional[np.ndarray]:
        """
        获取图像中最大人脸的关键点
        
        Args:
            image: 输入图像
            
        Returns:
            68个关键点坐标或None
        """
        # 首先检测人脸
        from .face_detector import FaceDetector
        
        detector = FaceDetector(method='face_recognition')  # 使用face_recognition检测
        face_box = detector.get_largest_face(image)
        
        if face_box is None:
            return None
        
        # 提取关键点
        landmarks = self.extract_landmarks(image, face_box)
        return landmarks
    
    def draw_landmarks(self, image: np.ndarray, landmarks: np.ndarray) -> np.ndarray:
        """
        在图像上绘制关键点
        
        Args:
            image: 输入图像
            landmarks: 关键点坐标
            
        Returns:
            绘制了关键点的图像
        """
        result = image.copy()
        
        # 绘制所有关键点
        for i, (x, y) in enumerate(landmarks):
            cv2.circle(result, (int(x), int(y)), 2, (0, 255, 0), -1)
            # 可选：显示点的编号
            # cv2.putText(result, str(i), (int(x), int(y)), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
        
        # 绘制面部轮廓线
        self._draw_face_contours(result, landmarks)
        
        return result
    
    def _draw_face_contours(self, image: np.ndarray, landmarks: np.ndarray):
        """绘制面部轮廓线"""
        # 定义面部各部分的点索引
        contours = {
            'jaw': list(range(0, 17)),           # 下巴轮廓
            'left_eyebrow': list(range(17, 22)), # 左眉毛
            'right_eyebrow': list(range(22, 27)), # 右眉毛
            'nose': list(range(27, 36)),         # 鼻子
            'left_eye': list(range(36, 42)),     # 左眼
            'right_eye': list(range(42, 48)),    # 右眼
            'outer_lip': list(range(48, 60)),    # 外唇
            'inner_lip': list(range(60, 68))     # 内唇
        }
        
        colors = {
            'jaw': (255, 0, 0),
            'left_eyebrow': (0, 255, 0),
            'right_eyebrow': (0, 255, 0),
            'nose': (0, 0, 255),
            'left_eye': (255, 255, 0),
            'right_eye': (255, 255, 0),
            'outer_lip': (255, 0, 255),
            'inner_lip': (0, 255, 255)
        }
        
        for part, indices in contours.items():
            points = landmarks[indices].astype(np.int32)
            
            if part in ['left_eye', 'right_eye', 'outer_lip', 'inner_lip']:
                # 闭合轮廓
                cv2.polylines(image, [points], True, colors[part], 1)
            else:
                # 开放轮廓
                cv2.polylines(image, [points], False, colors[part], 1)
    
    def get_key_points(self, landmarks: np.ndarray) -> Dict[str, np.ndarray]:
        """
        获取关键的面部特征点
        
        Args:
            landmarks: 68个关键点
            
        Returns:
            关键特征点字典
        """
        key_points = {
            'left_eye_center': np.mean(landmarks[36:42], axis=0),
            'right_eye_center': np.mean(landmarks[42:48], axis=0),
            'nose_tip': landmarks[30],
            'mouth_center': np.mean(landmarks[48:68], axis=0),
            'chin_center': landmarks[8],
            'left_mouth_corner': landmarks[48],
            'right_mouth_corner': landmarks[54]
        }
        
        return key_points


def test_landmark_extractor():
    """测试关键点提取器"""
    # 创建测试图像
    test_image = np.zeros((480, 640, 3), dtype=np.uint8)
    test_image[:] = (128, 128, 128)
    
    try:
        extractor = LandmarkExtractor(method='face_recognition')
        landmarks = extractor.get_face_landmarks(test_image)
        
        if landmarks is not None:
            print(f"成功提取 {len(landmarks)} 个关键点")
            key_points = extractor.get_key_points(landmarks)
            print("关键特征点:", list(key_points.keys()))
        else:
            print("未检测到人脸或关键点")
            
    except Exception as e:
        print(f"关键点提取器测试失败: {e}")


if __name__ == "__main__":
    test_landmark_extractor()

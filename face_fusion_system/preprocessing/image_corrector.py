"""
图像校正模块
实现图像的几何校正、尺寸标准化等预处理功能
"""

import cv2
import numpy as np
from typing import Tuple, Optional, List
import math


class ImageCorrector:
    """图像校正器"""
    
    def __init__(self, target_size: Tuple[int, int] = (512, 512)):
        """
        初始化图像校正器
        
        Args:
            target_size: 目标图像尺寸 (width, height)
        """
        self.target_size = target_size
    
    def resize_image(self, image: np.ndarray, keep_aspect_ratio: bool = True) -> np.ndarray:
        """
        调整图像尺寸
        
        Args:
            image: 输入图像
            keep_aspect_ratio: 是否保持宽高比
            
        Returns:
            调整尺寸后的图像
        """
        if keep_aspect_ratio:
            return self._resize_keep_aspect_ratio(image)
        else:
            return cv2.resize(image, self.target_size)
    
    def _resize_keep_aspect_ratio(self, image: np.ndarray) -> np.ndarray:
        """保持宽高比的图像缩放"""
        h, w = image.shape[:2]
        target_w, target_h = self.target_size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        
        # 计算新的尺寸
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 缩放图像
        resized = cv2.resize(image, (new_w, new_h))
        
        # 创建目标尺寸的画布
        result = np.zeros((target_h, target_w, 3), dtype=np.uint8)
        
        # 计算居中位置
        start_x = (target_w - new_w) // 2
        start_y = (target_h - new_h) // 2
        
        # 将缩放后的图像放置在画布中心
        result[start_y:start_y + new_h, start_x:start_x + new_w] = resized
        
        return result
    
    def crop_face_region(self, image: np.ndarray, face_box: Tuple[int, int, int, int], 
                        padding_ratio: float = 0.3) -> np.ndarray:
        """
        裁剪人脸区域
        
        Args:
            image: 输入图像
            face_box: 人脸边界框 (x, y, w, h)
            padding_ratio: 边距比例
            
        Returns:
            裁剪后的人脸图像
        """
        x, y, w, h = face_box
        
        # 计算边距
        padding_w = int(w * padding_ratio)
        padding_h = int(h * padding_ratio)
        
        # 计算裁剪区域
        crop_x = max(0, x - padding_w)
        crop_y = max(0, y - padding_h)
        crop_w = min(image.shape[1] - crop_x, w + 2 * padding_w)
        crop_h = min(image.shape[0] - crop_y, h + 2 * padding_h)
        
        # 裁剪图像
        cropped = image[crop_y:crop_y + crop_h, crop_x:crop_x + crop_w]
        
        return cropped
    
    def align_face_by_eyes(self, image: np.ndarray, left_eye: Tuple[float, float], 
                          right_eye: Tuple[float, float]) -> np.ndarray:
        """
        根据眼睛位置对齐人脸
        
        Args:
            image: 输入图像
            left_eye: 左眼中心坐标
            right_eye: 右眼中心坐标
            
        Returns:
            对齐后的图像
        """
        # 计算眼睛之间的角度
        dx = right_eye[0] - left_eye[0]
        dy = right_eye[1] - left_eye[1]
        angle = math.degrees(math.atan2(dy, dx))
        
        # 计算眼睛中心点
        eye_center = ((left_eye[0] + right_eye[0]) / 2, (left_eye[1] + right_eye[1]) / 2)
        
        # 创建旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(eye_center, angle, 1.0)
        
        # 应用旋转
        aligned = cv2.warpAffine(image, rotation_matrix, (image.shape[1], image.shape[0]))
        
        return aligned
    
    def preprocess_side_face(self, image: np.ndarray, face_box: Tuple[int, int, int, int],
                           landmarks: Optional[np.ndarray] = None) -> np.ndarray:
        """
        侧脸图像预处理流程
        
        Args:
            image: 输入侧脸图像
            face_box: 人脸边界框
            landmarks: 面部关键点（可选）
            
        Returns:
            预处理后的图像
        """
        # 1. 裁剪人脸区域
        cropped = self.crop_face_region(image, face_box)
        
        # 2. 如果有关键点，进行对齐和校正
        if landmarks is not None:
            # 调整关键点坐标到裁剪后的图像
            x, y, w, h = face_box
            padding_w = int(w * 0.3)
            padding_h = int(h * 0.3)
            crop_x = max(0, x - padding_w)
            crop_y = max(0, y - padding_h)
            
            adjusted_landmarks = landmarks.copy()
            adjusted_landmarks[:, 0] -= crop_x
            adjusted_landmarks[:, 1] -= crop_y
            
            # 眼睛对齐
            if len(adjusted_landmarks) >= 48:
                left_eye = np.mean(adjusted_landmarks[36:42], axis=0)
                right_eye = np.mean(adjusted_landmarks[42:48], axis=0)
                cropped = self.align_face_by_eyes(cropped, left_eye, right_eye)
        
        # 3. 调整到目标尺寸
        result = self.resize_image(cropped, keep_aspect_ratio=True)
        
        return result


def test_image_corrector():
    """测试图像校正器"""
    # 创建测试图像
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    corrector = ImageCorrector(target_size=(256, 256))
    
    # 测试调整尺寸
    resized = corrector.resize_image(test_image)
    print(f"原始尺寸: {test_image.shape}, 调整后尺寸: {resized.shape}")
    
    # 测试人脸裁剪
    face_box = (100, 100, 200, 200)  # 模拟人脸框
    cropped = corrector.crop_face_region(test_image, face_box)
    print(f"裁剪后尺寸: {cropped.shape}")
    
    # 测试预处理流程
    processed = corrector.preprocess_side_face(test_image, face_box)
    print(f"预处理后尺寸: {processed.shape}")
    
    print("图像校正器测试完成")


if __name__ == "__main__":
    test_image_corrector()

"""
侧脸融合正脸演示程序
展示完整的双摄像头侧脸融合流程
"""

import cv2
import numpy as np
import os
import sys
import time
from typing import Tuple, Optional

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fusion.basic_fusion import BasicFusion


class FaceFusionDemo:
    """侧脸融合演示器"""
    
    def __init__(self):
        """初始化演示器"""
        self.basic_fusion = BasicFusion()
        self.left_camera = None
        self.right_camera = None
        
    def create_demo_images(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建演示用的侧脸图像
        
        Returns:
            (左侧脸图像, 右侧脸图像)
        """
        print("创建演示图像...")
        
        # 创建模拟的侧脸图像
        height, width = 480, 640
        
        # 左侧脸图像
        left_image = np.ones((height, width, 3), dtype=np.uint8) * 120
        
        # 绘制左侧脸
        face_center_x = width // 3
        face_center_y = height // 2
        
        # 脸部轮廓
        cv2.ellipse(left_image, (face_center_x, face_center_y), (100, 150), 0, -45, 225, (200, 180, 160), -1)
        
        # 眼睛
        cv2.circle(left_image, (face_center_x - 30, face_center_y - 40), 12, (50, 50, 50), -1)
        cv2.circle(left_image, (face_center_x + 15, face_center_y - 35), 10, (50, 50, 50), -1)
        cv2.circle(left_image, (face_center_x - 30, face_center_y - 40), 6, (255, 255, 255), -1)
        cv2.circle(left_image, (face_center_x + 15, face_center_y - 35), 5, (255, 255, 255), -1)
        
        # 鼻子
        nose_points = np.array([
            [face_center_x, face_center_y - 15],
            [face_center_x + 20, face_center_y + 5],
            [face_center_x + 15, face_center_y + 15],
            [face_center_x - 5, face_center_y + 10]
        ], np.int32)
        cv2.fillPoly(left_image, [nose_points], (160, 140, 120))
        
        # 嘴巴
        cv2.ellipse(left_image, (face_center_x + 10, face_center_y + 50), (25, 12), 0, 0, 180, (120, 80, 80), 3)
        
        # 头发
        cv2.ellipse(left_image, (face_center_x, face_center_y - 80), (120, 80), 0, 180, 360, (80, 60, 40), -1)
        
        # 右侧脸图像
        right_image = np.ones((height, width, 3), dtype=np.uint8) * 120
        
        # 绘制右侧脸
        face_center_x = 2 * width // 3
        
        # 脸部轮廓
        cv2.ellipse(right_image, (face_center_x, face_center_y), (100, 150), 0, -45, 225, (200, 180, 160), -1)
        
        # 眼睛
        cv2.circle(right_image, (face_center_x - 15, face_center_y - 35), 10, (50, 50, 50), -1)
        cv2.circle(right_image, (face_center_x + 30, face_center_y - 40), 12, (50, 50, 50), -1)
        cv2.circle(right_image, (face_center_x - 15, face_center_y - 35), 5, (255, 255, 255), -1)
        cv2.circle(right_image, (face_center_x + 30, face_center_y - 40), 6, (255, 255, 255), -1)
        
        # 鼻子
        nose_points = np.array([
            [face_center_x, face_center_y - 15],
            [face_center_x - 20, face_center_y + 5],
            [face_center_x - 15, face_center_y + 15],
            [face_center_x + 5, face_center_y + 10]
        ], np.int32)
        cv2.fillPoly(right_image, [nose_points], (160, 140, 120))
        
        # 嘴巴
        cv2.ellipse(right_image, (face_center_x - 10, face_center_y + 50), (25, 12), 0, 0, 180, (120, 80, 80), 3)
        
        # 头发
        cv2.ellipse(right_image, (face_center_x, face_center_y - 80), (120, 80), 0, 180, 360, (80, 60, 40), -1)
        
        return left_image, right_image
    
    def initialize_cameras(self) -> bool:
        """
        初始化双摄像头
        
        Returns:
            是否成功初始化
        """
        print("尝试初始化摄像头...")
        
        try:
            # 尝试打开两个摄像头
            self.left_camera = cv2.VideoCapture(0)
            self.right_camera = cv2.VideoCapture(1)
            
            if self.left_camera.isOpened() and self.right_camera.isOpened():
                print("双摄像头初始化成功")
                return True
            elif self.left_camera.isOpened():
                print("只检测到一个摄像头，将使用演示模式")
                self.right_camera = None
                return True
            else:
                print("未检测到摄像头，将使用演示图像")
                self.left_camera = None
                self.right_camera = None
                return False
                
        except Exception as e:
            print(f"摄像头初始化失败: {e}")
            return False
    
    def capture_frames(self) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        捕获双摄像头帧
        
        Returns:
            (左摄像头帧, 右摄像头帧)
        """
        left_frame = None
        right_frame = None
        
        if self.left_camera and self.left_camera.isOpened():
            ret_left, left_frame = self.left_camera.read()
            if not ret_left:
                left_frame = None
        
        if self.right_camera and self.right_camera.isOpened():
            ret_right, right_frame = self.right_camera.read()
            if not ret_right:
                right_frame = None
        
        return left_frame, right_frame
    
    def process_and_fuse(self, left_image: np.ndarray, right_image: np.ndarray) -> np.ndarray:
        """
        处理并融合图像
        
        Args:
            left_image: 左侧图像
            right_image: 右侧图像
            
        Returns:
            融合后的图像
        """
        # 调整图像尺寸
        target_size = (400, 400)
        left_resized = cv2.resize(left_image, target_size)
        right_resized = cv2.resize(right_image, target_size)
        
        # 简单的光照均衡
        left_normalized = cv2.convertScaleAbs(left_resized, alpha=1.2, beta=10)
        right_normalized = cv2.convertScaleAbs(right_resized, alpha=1.2, beta=10)
        
        # 融合图像
        fused = self.basic_fusion.seamless_fusion(left_normalized, right_normalized)
        
        return fused
    
    def run_demo(self):
        """运行演示"""
        print("=== 侧脸融合正脸演示 ===")
        
        # 初始化摄像头
        camera_available = self.initialize_cameras()
        
        # 创建窗口
        cv2.namedWindow('Left Camera', cv2.WINDOW_AUTOSIZE)
        cv2.namedWindow('Right Camera', cv2.WINDOW_AUTOSIZE)
        cv2.namedWindow('Fused Result', cv2.WINDOW_AUTOSIZE)
        
        print("按 'q' 退出演示")
        print("按 's' 保存当前融合结果")
        
        frame_count = 0
        
        try:
            while True:
                if camera_available:
                    # 从摄像头捕获
                    left_frame, right_frame = self.capture_frames()
                    
                    if left_frame is None or right_frame is None:
                        # 如果摄像头失败，使用演示图像
                        left_frame, right_frame = self.create_demo_images()
                else:
                    # 使用演示图像
                    left_frame, right_frame = self.create_demo_images()
                    
                    # 添加一些动态效果
                    offset = int(20 * np.sin(frame_count * 0.1))
                    left_frame = np.roll(left_frame, offset, axis=1)
                    right_frame = np.roll(right_frame, -offset, axis=1)
                
                # 处理和融合
                fused_result = self.process_and_fuse(left_frame, right_frame)
                
                # 添加文本信息
                cv2.putText(left_frame, 'Left Side Face', (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(right_frame, 'Right Side Face', (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(fused_result, 'Fused Front Face', (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                # 显示图像
                cv2.imshow('Left Camera', left_frame)
                cv2.imshow('Right Camera', right_frame)
                cv2.imshow('Fused Result', fused_result)
                
                # 处理按键
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    # 保存结果
                    timestamp = int(time.time())
                    cv2.imwrite(f'fusion_result_{timestamp}.jpg', fused_result)
                    print(f"保存融合结果: fusion_result_{timestamp}.jpg")
                
                frame_count += 1
                
                # 如果不是实时摄像头，添加延迟
                if not camera_available:
                    time.sleep(0.1)
                    
        except KeyboardInterrupt:
            print("用户中断演示")
        
        finally:
            # 清理资源
            if self.left_camera:
                self.left_camera.release()
            if self.right_camera:
                self.right_camera.release()
            cv2.destroyAllWindows()
            print("演示结束")
    
    def run_static_demo(self):
        """运行静态演示"""
        print("=== 静态图像融合演示 ===")
        
        # 创建演示图像
        left_image, right_image = self.create_demo_images()
        
        # 处理和融合
        fused_result = self.process_and_fuse(left_image, right_image)
        
        # 保存结果
        os.makedirs('demo_results', exist_ok=True)
        cv2.imwrite('demo_results/left_side.jpg', left_image)
        cv2.imwrite('demo_results/right_side.jpg', right_image)
        cv2.imwrite('demo_results/fused_front.jpg', fused_result)
        
        print("静态演示完成，结果保存在 demo_results 目录")
        print(f"融合结果尺寸: {fused_result.shape}")
        
        # 显示结果
        cv2.imshow('Left Side Face', left_image)
        cv2.imshow('Right Side Face', right_image)
        cv2.imshow('Fused Front Face', fused_result)
        
        print("按任意键关闭窗口...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()


def main():
    """主函数"""
    demo = FaceFusionDemo()
    
    print("选择演示模式:")
    print("1. 实时演示 (需要摄像头)")
    print("2. 静态演示")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == '1':
            demo.run_demo()
        elif choice == '2':
            demo.run_static_demo()
        else:
            print("无效选择，运行静态演示")
            demo.run_static_demo()
            
    except KeyboardInterrupt:
        print("\n用户取消")
    except Exception as e:
        print(f"演示过程中出现错误: {e}")


if __name__ == "__main__":
    main()

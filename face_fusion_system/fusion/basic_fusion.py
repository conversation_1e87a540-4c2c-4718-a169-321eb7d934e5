"""
基础图像融合模块
"""

import cv2
import numpy as np
from typing import Tuple, Optional

class BasicFusion:
    def __init__(self):
        pass
    
    def weighted_average_fusion(self, left_image: np.ndarray, right_image: np.ndarray,
                               left_weight: float = 0.5) -> np.ndarray:
        """加权平均融合"""
        right_weight = 1.0 - left_weight
        
        if left_image.shape != right_image.shape:
            right_image = cv2.resize(right_image, (left_image.shape[1], left_image.shape[0]))
        
        fused = cv2.addWeighted(left_image, left_weight, right_image, right_weight, 0)
        return fused
    
    def seamless_fusion(self, left_image: np.ndarray, right_image: np.ndarray,
                       blend_width: int = 50) -> np.ndarray:
        """无缝融合"""
        if left_image.shape != right_image.shape:
            right_image = cv2.resize(right_image, (left_image.shape[1], left_image.shape[0]))
        
        h, w = left_image.shape[:2]
        mask = np.zeros((h, w), dtype=np.float32)
        
        # 左半部分使用左图
        mask[:, :w//2 - blend_width//2] = 1.0
        
        # 中间部分平滑过渡
        for i in range(blend_width):
            col = w//2 - blend_width//2 + i
            if 0 <= col < w:
                weight = 1.0 - (i / blend_width)
                mask[:, col] = weight
        
        mask = np.stack([mask] * 3, axis=2)
        fused = mask * left_image + (1 - mask) * right_image
        
        return fused.astype(np.uint8)

def test_basic_fusion():
    print("测试基础融合器...")
    left_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    right_image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
    
    fusion = BasicFusion()
    
    weighted_result = fusion.weighted_average_fusion(left_image, right_image, 0.6)
    print(f"加权融合结果尺寸: {weighted_result.shape}")
    
    seamless_result = fusion.seamless_fusion(left_image, right_image)
    print(f"无缝融合结果尺寸: {seamless_result.shape}")
    
    print("基础融合器测试完成")

if __name__ == "__main__":
    test_basic_fusion()

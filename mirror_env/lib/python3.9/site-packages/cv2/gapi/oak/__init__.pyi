__all__: list[str] = []

EncoderConfig_RateControlMode_CBR: int
ENCODER_CONFIG_RATE_CONTROL_MODE_CBR: int
EncoderConfig_RateControlMode_VBR: int
ENCODER_CONFIG_RATE_CONTROL_MODE_VBR: int
EncoderConfig_RateControlMode = int
"""One of [EncoderConfig_RateControlMode_CBR, ENCODER_CONFIG_RATE_CONTROL_MODE_CBR, EncoderConfig_RateControlMode_VBR, ENCODER_CONFIG_RATE_CONTROL_MODE_VBR]"""

EncoderConfig_Profile_H264_BASELINE: int
ENCODER_CONFIG_PROFILE_H264_BASELINE: int
EncoderConfig_Profile_H264_HIGH: int
ENCODER_CONFIG_PROFILE_H264_HIGH: int
EncoderConfig_Profile_H264_MAIN: int
ENCODER_CONFIG_PROFILE_H264_MAIN: int
EncoderConfig_Profile_H265_MAIN: int
ENCODER_CONFIG_PROFILE_H265_MAIN: int
EncoderConfig_Profile_MJPEG: int
ENCODER_CONFIG_PROFILE_MJPEG: int
EncoderConfig_Profile = int
"""One of [EncoderConfig_Profile_H264_BASELINE, ENCODER_CONFIG_PROFILE_H264_BASELINE, EncoderConfig_Profile_H264_HIGH, ENCODER_CONFIG_PROFILE_H264_HIGH, EncoderConfig_Profile_H264_MAIN, ENCODER_CONFIG_PROFILE_H264_MAIN, EncoderConfig_Profile_H265_MAIN, ENCODER_CONFIG_PROFILE_H265_MAIN, EncoderConfig_Profile_MJPEG, ENCODER_CONFIG_PROFILE_MJPEG]"""

ColorCameraParams_BoardSocket_RGB: int
COLOR_CAMERA_PARAMS_BOARD_SOCKET_RGB: int
ColorCameraParams_BoardSocket_BGR: int
COLOR_CAMERA_PARAMS_BOARD_SOCKET_BGR: int
ColorCameraParams_BoardSocket = int
"""One of [ColorCameraParams_BoardSocket_RGB, COLOR_CAMERA_PARAMS_BOARD_SOCKET_RGB, ColorCameraParams_BoardSocket_BGR, COLOR_CAMERA_PARAMS_BOARD_SOCKET_BGR]"""

ColorCameraParams_Resolution_THE_1080_P: int
COLOR_CAMERA_PARAMS_RESOLUTION_THE_1080_P: int
ColorCameraParams_Resolution = int
"""One of [ColorCameraParams_Resolution_THE_1080_P, COLOR_CAMERA_PARAMS_RESOLUTION_THE_1080_P]"""


# Classes

